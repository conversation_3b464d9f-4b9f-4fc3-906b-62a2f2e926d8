"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from "react";
import { useSession } from "next-auth/react";

interface Notification {
  id: string;
  title: string;
  message: string;
  type: "INFO" | "SUCCESS" | "WARNING" | "ERROR" | "SYSTEM";
  priority: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  targetType: string;
  targetId?: string;
  isRead: boolean;
  readAt?: string;
  actionUrl?: string;
  metadata?: any;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
  creator?: {
    id: string;
    name: string;
    email: string;
  };
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
  createNotification: (notification: Partial<Notification>) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export function useNotifications() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider");
  }
  return context;
}

interface NotificationProviderProps {
  children: React.ReactNode;
}

export function NotificationProvider({ children }: NotificationProviderProps) {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [eventSource, setEventSource] = useState<EventSource | null>(null);

  // Initialize SSE connection
  const initializeSSE = useCallback(() => {
    if (!session?.user || session.user.type !== "admin") {
      return;
    }

    // Close existing connection
    if (eventSource) {
      eventSource.close();
    }

    const newEventSource = new EventSource("/api/admin/notifications/stream");

    newEventSource.onopen = () => {
      setIsConnected(true);
      setError(null);
      console.log("Notification stream connected");
    };

    newEventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
          case "connection":
            console.log("Notification stream established");
            break;
            
          case "unread_count":
            setUnreadCount(data.count);
            break;
            
          case "latest_notifications":
            setNotifications(data.notifications);
            setIsLoading(false);
            break;
            
          case "heartbeat":
            // Keep connection alive
            break;
            
          default:
            console.log("Unknown notification event:", data);
        }
      } catch (err) {
        console.error("Error parsing SSE data:", err);
      }
    };

    newEventSource.onerror = (event) => {
      console.error("Notification stream error:", event);
      setIsConnected(false);
      setError("Connection to notification stream failed");
      
      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        if (session?.user && session.user.type === "admin") {
          initializeSSE();
        }
      }, 5000);
    };

    setEventSource(newEventSource);
  }, [session, eventSource]);

  // Initialize connection when session is available
  useEffect(() => {
    if (session?.user && session.user.type === "admin") {
      initializeSSE();
    }

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, [session]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  }, []);

  // Fetch notifications manually
  const refreshNotifications = useCallback(async () => {
    if (!session?.user || session.user.type !== "admin") {
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/notifications?limit=10");
      
      if (!response.ok) {
        throw new Error("Failed to fetch notifications");
      }

      const data = await response.json();
      setNotifications(data.notifications || []);
      
      // Calculate unread count
      const unread = (data.notifications || []).filter((n: Notification) => !n.isRead).length;
      setUnreadCount(unread);
      
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch notifications");
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/admin/notifications/${notificationId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isRead: true }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark notification as read");
      }

      // Update local state
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, isRead: true, readAt: new Date().toISOString() }
            : n
        )
      );

      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error("Error marking notification as read:", err);
      throw err;
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      const unreadIds = notifications.filter(n => !n.isRead).map(n => n.id);
      
      if (unreadIds.length === 0) {
        return;
      }

      const response = await fetch("/api/admin/notifications/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "mark_read",
          notificationIds: unreadIds,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to mark all notifications as read");
      }

      // Update local state
      setNotifications(prev => 
        prev.map(n => ({ ...n, isRead: true, readAt: new Date().toISOString() }))
      );
      setUnreadCount(0);
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      throw err;
    }
  }, [notifications]);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: string) => {
    try {
      const response = await fetch(`/api/admin/notifications/${notificationId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete notification");
      }

      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      if (deletedNotification && !deletedNotification.isRead) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (err) {
      console.error("Error deleting notification:", err);
      throw err;
    }
  }, [notifications]);

  // Create new notification
  const createNotification = useCallback(async (notification: Partial<Notification>) => {
    try {
      const response = await fetch("/api/admin/notifications", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(notification),
      });

      if (!response.ok) {
        throw new Error("Failed to create notification");
      }

      const data = await response.json();
      
      // Add to local state if it targets current user
      const newNotification = data.notification;
      const shouldShow = 
        newNotification.targetType === "ALL_ADMINS" ||
        (newNotification.targetType === "SPECIFIC_ADMIN" && newNotification.targetId === session?.user?.id) ||
        newNotification.targetType === `ROLE_${session?.user?.role}`;

      if (shouldShow) {
        setNotifications(prev => [newNotification, ...prev]);
        if (!newNotification.isRead) {
          setUnreadCount(prev => prev + 1);
        }
      }
    } catch (err) {
      console.error("Error creating notification:", err);
      throw err;
    }
  }, [session]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
    createNotification,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}
