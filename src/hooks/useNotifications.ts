"use client";

import { useContext } from "react";
import { NotificationContext } from "@/contexts/NotificationContext";

export function useNotifications() {
  const context = useContext(NotificationContext);
  
  if (context === undefined) {
    throw new Error("useNotifications must be used within a NotificationProvider");
  }
  
  return context;
}

// Additional hook for notification utilities
export function useNotificationUtils() {
  const { createNotification } = useNotifications();

  const showSuccess = (title: string, message: string, options?: {
    actionUrl?: string;
    metadata?: any;
    targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
    targetId?: string;
  }) => {
    return createNotification({
      title,
      message,
      type: "SUCCESS",
      priority: "NORMAL",
      ...options,
    });
  };

  const showError = (title: string, message: string, options?: {
    actionUrl?: string;
    metadata?: any;
    targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
    targetId?: string;
  }) => {
    return createNotification({
      title,
      message,
      type: "ERROR",
      priority: "HIGH",
      ...options,
    });
  };

  const showWarning = (title: string, message: string, options?: {
    actionUrl?: string;
    metadata?: any;
    targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
    targetId?: string;
  }) => {
    return createNotification({
      title,
      message,
      type: "WARNING",
      priority: "NORMAL",
      ...options,
    });
  };

  const showInfo = (title: string, message: string, options?: {
    actionUrl?: string;
    metadata?: any;
    targetType?: "ALL_ADMINS" | "SPECIFIC_ADMIN" | "ROLE_BASED";
    targetId?: string;
  }) => {
    return createNotification({
      title,
      message,
      type: "INFO",
      priority: "LOW",
      ...options,
    });
  };

  const showSystemAlert = (title: string, message: string, options?: {
    actionUrl?: string;
    metadata?: any;
    priority?: "LOW" | "NORMAL" | "HIGH" | "URGENT";
  }) => {
    return createNotification({
      title,
      message,
      type: "SYSTEM",
      priority: options?.priority || "HIGH",
      targetType: "ALL_ADMINS",
      actionUrl: options?.actionUrl,
      metadata: options?.metadata,
    });
  };

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showSystemAlert,
  };
}
